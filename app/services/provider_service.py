import grpc
from typing import Optional
from app.core.config import settings
from app.grpc_ import provider_pb2, provider_pb2_grpc
from fastapi import HTTPException


class ProviderServiceClient:
    """Client for communicating with the Provider gRPC service."""

    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.AGENT_SERVICE_HOST}:{settings.AGENT_SERVICE_PORT}"
        )
        self.stub = provider_pb2_grpc.ProviderServiceStub(self.channel)

    def _handle_error(self, e: grpc.RpcError):
        """Handle gRPC errors and convert them to HTTP exceptions."""
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")

    async def create_provider(
        self,
        provider: str,
        description: Optional[str] = None,
        base_url: str = None,
        is_active: bool = True,
        is_default: bool = False
    ):
        """Create a new provider."""
        try:
            request = provider_pb2.CreateProviderRequest(
                name=provider,
                description=description or "",
                is_default=is_default
            )
            response = self.stub.CreateProvider(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_provider(self, provider_id: str):
        """Get a provider by ID."""
        try:
            request = provider_pb2.GetProviderRequest(id=provider_id)
            response = self.stub.GetProvider(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_providers(
        self,
        page: int = 1,
        page_size: int = 10,
        is_active: Optional[bool] = None
    ):
        """List all providers with pagination."""
        try:
            request = provider_pb2.GetAllProvidersRequest(
                page=page,
                page_size=page_size
            )
            
            if is_active is not None:
                request.is_default = is_active  # Using is_default as filter for now
            
            response = self.stub.GetAllProviders(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_provider(
        self,
        provider_id: str,
        provider: Optional[str] = None,
        description: Optional[str] = None,
        base_url: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_default: Optional[bool] = None
    ):
        """Update a provider."""
        try:
            request = provider_pb2.UpdateProviderRequest(
                id=provider_id
            )
            
            if provider is not None:
                request.name = provider
            if description is not None:
                request.description = description
            if is_default is not None:
                request.is_default = is_default
                
            response = self.stub.UpdateProvider(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_provider(self, provider_id: str):
        """Delete a provider."""
        try:
            request = provider_pb2.DeleteProviderRequest(id=provider_id)
            response = self.stub.DeleteProvider(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    def close(self):
        """Close the gRPC channel."""
        if hasattr(self, 'channel'):
            self.channel.close()


class ModelServiceClient:
    """Client for communicating with the Model gRPC service."""

    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.AGENT_SERVICE_HOST}:{settings.AGENT_SERVICE_PORT}"
        )
        self.stub = provider_pb2_grpc.ModelServiceStub(self.channel)

    def _handle_error(self, e: grpc.RpcError):
        """Handle gRPC errors and convert them to HTTP exceptions."""
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        elif status_code == grpc.StatusCode.FAILED_PRECONDITION:
            raise HTTPException(status_code=412, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail="Internal server error")

    async def create_model(
        self,
        provider_id: str,
        model: str,
        model_id: str,
        description: Optional[str] = None,
        input_price_per_token: Optional[float] = None,
        output_price_per_token: Optional[float] = None,
        max_tokens: Optional[int] = None,
        context_window: Optional[int] = None,
        temperature: float = 0.7,
        provider_type: str = "chat",
        is_active: bool = True,
        is_default: bool = False
    ):
        """Create a new model."""
        try:
            request = provider_pb2.CreateModelRequest(
                provider_id=provider_id,
                name=model,
                description=description or "",
                model_type=provider_type,
                is_default=is_default
            )
            if input_price_per_token is not None:
                request.input_price_per_token = input_price_per_token
            if output_price_per_token is not None:
                request.output_price_per_token = output_price_per_token
            if context_window is not None:
                request.context_window = context_window
            response = self.stub.CreateModel(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_model(self, model_id: str):
        """Get a model by ID."""
        try:
            request = provider_pb2.GetModelRequest(id=model_id)
            response = self.stub.GetModel(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_models(
        self,
        page: int = 1,
        page_size: int = 10,
        is_active: Optional[bool] = None,
        provider_id: Optional[str] = None
    ):
        """List all models with pagination."""
        try:
            request = provider_pb2.ListRequest(
                page=page,
                pageSize=page_size
            )
            
            if is_active is not None:
                request.isActive = is_active
            
            if provider_id is not None:
                request.providerId = provider_id
            
            response = self.stub.ListModels(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_models_by_provider(
        self,
        provider_id: str,
        page: int = 1,
        page_size: int = 10,
        is_active: Optional[bool] = None
    ):
        """List models for a specific provider."""
        try:
            request = provider_pb2.GetAllModelsRequest(
                page=page,
                page_size=page_size,
                provider_id=provider_id
            )
            
            if is_active is not None:
                request.is_default = is_active  # Using is_default as filter for now
            
            response = self.stub.GetAllModels(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_model(
        self,
        model_id: str,
        provider_id: Optional[str] = None,
        model: Optional[str] = None,
        model_id_field: Optional[str] = None,
        description: Optional[str] = None,
        input_price_per_token: Optional[float] = None,
        output_price_per_token: Optional[float] = None,
        max_tokens: Optional[int] = None,
        context_window: Optional[int] = None,
        temperature: Optional[float] = None,
        provider_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        is_default: Optional[bool] = None
    ):
        """Update a model."""
        try:
            request = provider_pb2.UpdateModelRequest(
                id=model_id
            )
            
            if model is not None:
                request.name = model
            if description is not None:
                request.description = description
            if provider_type is not None:
                request.model_type = provider_type
            if is_default is not None:
                request.is_default = is_default
            if input_price_per_token is not None:
                request.input_price_per_token = input_price_per_token
            if output_price_per_token is not None:
                request.output_price_per_token = output_price_per_token
            if context_window is not None:
                request.context_window = context_window
                
            response = self.stub.UpdateModel(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_model(self, model_id: str):
        """Delete a model."""
        try:
            request = provider_pb2.DeleteModelRequest(id=model_id)
            response = self.stub.DeleteModel(request)
            return response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    def close(self):
        """Close the gRPC channel."""
        if hasattr(self, 'channel'):
            self.channel.close()
